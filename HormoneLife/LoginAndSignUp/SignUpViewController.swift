//
//  SignUpViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/5/26.
//

import UIKit

class SignUpViewController: BaseViewController {

    @IBOutlet weak var tipsBgView: UIView!
    @IBOutlet weak var tAndcTipsView: UIView!
    @IBOutlet weak var agreeCheckBox: UIButton!
    @IBOutlet weak var tAndcTextView: UITextView!
    @IBOutlet weak var tAndcDescriptionTextView: UITextView!
    
    @IBOutlet weak var firstNameField: UITextField!
    @IBOutlet weak var lastNameField: UITextField!
    @IBOutlet weak var emailField: UITextField!
//    @IBOutlet weak var authField: UITextField!
    @IBOutlet weak var passwordField: UITextField!
    @IBOutlet weak var confirmPwdField: UITextField!
    @IBOutlet weak var invitationCodeField: UITextField!
    @IBOutlet weak var signUpButton: UIButton!
    @IBOutlet weak var emailTipLabel: UILabel!
    @IBOutlet weak var accountTypeButton: UIButton!
//    @IBOutlet weak var sendAuthButton: UIButton!
    @IBOutlet weak var accountFieldHeight: NSLayoutConstraint!  //76 and 92
    @IBOutlet weak var disAgreeBtn: UIButton!
    @IBOutlet weak var agreeBtn: UIButton!
    
    @IBOutlet weak var accountLabelR: UILabel!
    @IBOutlet weak var accountLabelL: UILabel!
    
    var inputFields: [UITextField] {
        [firstNameField, lastNameField, emailField, passwordField, confirmPwdField, invitationCodeField]
    }
    
    let privacyPolicy = "Privacy Policy"
    let tAndcString = "Terms of Conditions"
    
    @IBOutlet weak var accountTypeSelectView: UIView!
    @IBOutlet weak var accountTypeSelectViewBottomContraint: NSLayoutConstraint!
    @IBOutlet weak var accountTypeSelectViewEmail: UIButton!
    @IBOutlet weak var accountTypeSelectViewMobile: UIButton!
    @IBOutlet weak var accountTypeSelectViewCloseButton: UIButton!
    
    let accountTypeSelectedViewColor = UIColor.mainTextColor
    
    var loginType: LoginType = .phone {
        didSet {
            accountTypeSelectViewEmail.isSelected = loginType == .email
            accountTypeSelectViewMobile.isSelected = loginType == .phone
            accountLabelR.text = loginType == .email ? "Use Mobile Instead" : "Use Email Instead"
            accountLabelL.text = loginType == .email ? "Email Address" : "Mobile Number"
            emailField.text = ""
            emailField.placeholder = loginType.accountFieldPlaceholder
            emailTipViewIsHidden = loginType == .phone
            if loginType == .email {
                accountTypeSelectViewEmail.backgroundColor = accountTypeSelectedViewColor
                accountTypeSelectViewMobile.backgroundColor = .white
                emailField.leftView = nil
                emailField.leftViewMode = .never
                emailField.keyboardType = .emailAddress
            } else if loginType == .phone {
                accountTypeSelectViewMobile.backgroundColor = accountTypeSelectedViewColor
                accountTypeSelectViewEmail.backgroundColor = .white
                emailField.leftView = areaNumLabel
                emailField.leftViewMode = .always
                emailField.keyboardType = .numberPad
            }
        }
    }
    
    private var areaNumLabel: UILabel = {
        let l = UILabel()
        l.textColor = .mainTextColor.withAlphaComponent(0.6)
        l.font = .regularGilroyFont(14)
        l.textAlignment = .left
        l.text = "+01    "
        return l
    }()
    
    var tempLoginType: LoginType = .phone
    
    var tipsViewIsHidden: Bool = false {
        didSet {
            tAndcTipsView.isHidden = tipsViewIsHidden
            tipsBgView.isHidden = tipsViewIsHidden
        }
    }
    
    var accountTypeSelectViewHidden: Bool = true {
        didSet {
            accountTypeSelectViewBottomContraint.constant = accountTypeSelectViewHidden ? -328 : 0
        }
    }
    
    var emailTipViewIsHidden: Bool = true {
        didSet {
            emailTipLabel.isHidden = emailTipViewIsHidden
            accountFieldHeight.constant = emailTipViewIsHidden ? 92 : 108
        }
    }
    
    var timer: Timer?
    var totalTime = 61
    
    override func viewDidLoad() {
        super.viewDidLoad()

        title = "Sign Up"
        
        setupUI()
        emailTipViewIsHidden = true
        accountTypeSelectViewHidden = true
        setupInputFields()
        setupSelectView()
        checkLoginButtonStatus()
        // 新增：默认隐藏条款提示遮罩，防止遮挡按钮
        tipsViewIsHidden = true
    }
    
    func setupUI() {
        disAgreeBtn.layer.cornerRadius = 4
        disAgreeBtn.layer.borderColor = UIColor.mainTextColor.cgColor
        disAgreeBtn.layer.borderWidth = 1
        
        agreeBtn.layer.cornerRadius = 4
        
        tAndcDescriptionTextView.underlineWords(words: [privacyPolicy, tAndcString])
        tAndcTextView.underlineWords(words: [privacyPolicy, tAndcString])
        //tAndcTextView.isSelectable = true  //一定要true才可点击
        
        inputFields.forEach {
            $0.setAttributedPlaceholer($0.placeholder)
        }
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationController?.navigationBar.isHidden = false
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        removeTimer()
    }
    
    private func setupInputFields() {
        inputFields.forEach {
            $0.delegate = self
        }
    }
    
    private func checkLoginButtonStatus() {
        // 获取除邀请码外的必填字段
        let requiredFields = [firstNameField, lastNameField, emailField, passwordField, confirmPwdField]
        let emptyFields = requiredFields.compactMap { $0 }.filter { $0.isEmpty }
        
        if loginType == .phone {
            emailField.keyboardType = .numberPad
            signUpButton.isEnabled = emptyFields.isEmpty && agreeCheckBox.isSelected && (passwordField.text == confirmPwdField.text)
        } else {
            emailField.keyboardType = .emailAddress
            signUpButton.isEnabled = emptyFields.isEmpty && agreeCheckBox.isSelected && (passwordField.text == confirmPwdField.text) && (emailField.text ?? "").contains("@")
        }
        
        let disableColor = UIColor(red: 68/255, green: 85/255, blue: 113/255, alpha: 1)
        let enableColor = UIColor.mainTextColor.withAlphaComponent(1)
        signUpButton.backgroundColor = signUpButton.isEnabled ? enableColor : disableColor
    }
    
    private func setupSelectView() {
        accountTypeSelectViewEmail.setTitleColor(.white, for: .selected)
        accountTypeSelectViewEmail.setTitleColor(accountTypeSelectedViewColor, for: .normal)
        accountTypeSelectViewEmail.layer.borderColor = accountTypeSelectedViewColor.cgColor
        accountTypeSelectViewEmail.layer.borderWidth = 1.0
        
        accountTypeSelectViewMobile.setTitleColor(.white, for: .selected)
        accountTypeSelectViewMobile.setTitleColor(accountTypeSelectedViewColor, for: .normal)
        accountTypeSelectViewMobile.layer.borderColor = accountTypeSelectedViewColor.cgColor
        accountTypeSelectViewMobile.layer.borderWidth = 1.0
        loginType = .phone
        
        accountLabelR.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(didTapAccountTypeButtonAction)))
    }
    
    @IBAction func agreeCheckBoxAction(_ sender: UIButton) {
        tipsViewIsHidden = false
    }
    
    @IBAction func disagreeButtonAction(_ sender: Any) {
        tipsViewIsHidden = true
        agreeCheckBox.isSelected = false
        checkLoginButtonStatus()
    }
    
    @IBAction func agreeButonAction(_ sender: Any) {
        tipsViewIsHidden = true
        agreeCheckBox.isSelected = true
        checkLoginButtonStatus()
    }
    
    @IBAction func closeTipsViewButtonAction(_ sender: Any) {
        tipsViewIsHidden = true
    }
    
    @IBAction func didTapSignUpAction(_ sender: Any) {
        // 收起键盘
        view.endEditing(true)

        // 基础校验，确保所有必填项都有值（邀请码不作为必填项）
        guard let account = emailField.text,
              let firstName = firstNameField.text,
              let lastName = lastNameField.text,
              let password = passwordField.text else { return }

        // 如果是邮箱注册，校验邮箱格式
        if loginType == .email {
            guard account.contains("@") else {
                showToachMessage(message: "Please input correct email address")
                return
            }
        }

        // 禁用注册按钮，防止重复点击
        signUpButton.isEnabled = false
        signUpButton.backgroundColor = UIColor(red: 68/255, green: 85/255, blue: 113/255, alpha: 1)

        // 先调用发送验证码API，检查账号是否已存在
        let sendValidation = SendValidate(account: account, codeType: loginType, messageCodeType: .REGISTER)
        print("[SignUp] 🚀 发送验证码请求 → account: \(account), loginType: \(loginType), firstName: \(firstName), lastName: \(lastName)")

        UserInteractor.sendValidateCode(sendValidation) { [weak self] success in
            guard let self = self else { return }

            print("[SignUp] 🎯 获取验证码接口返回 → success: \(success)")

            DispatchQueue.main.async {
                // 恢复注册按钮状态
                self.checkLoginButtonStatus()

                // 检查API调用是否成功
                if let isSuccess = success, isSuccess == true {
                    print("[SignUp] ✅ 验证码发送成功，跳转到验证码页面")
                    // 发送验证码成功，跳转到验证码页面
                    let verificationVC = VerificationCodeViewController()
                    verificationVC.account = account
                    verificationVC.firstName = firstName
                    verificationVC.lastName = lastName
                    verificationVC.password = password
                    verificationVC.invitationCode = self.invitationCodeField.text ?? ""
                    verificationVC.loginType = self.loginType

                    self.navigationController?.pushViewController(verificationVC, animated: true)
                } else {
                    print("[SignUp] 🚫 验证码发送失败，停留在注册页面 → success: \(success)")
                    // 发送验证码失败，可能是账号已存在或其他错误
                    // 停留在注册页面，错误信息已经通过 NetWorkHandle 显示
                }
            }
        }
    }
    
    @IBAction func didTapAccountTypeButtonAction(_ sender: Any) {
        tempLoginType = loginType
        self.accountTypeSelectView.isHidden = false
        self.accountTypeSelectView.showShadow()
        UIView.animate(withDuration: 0.35) {
            self.accountTypeSelectViewHidden = false
            self.view.layoutIfNeeded()
        }
    }
    
//    @IBAction func sendAuthAction(_ sender: Any) {
//        guard !emailField.isEmpty else { return }
//        if (emailField.text ?? "").contains("@") && loginType == .phone {
//            showToachMessage(message: "Please enter the correct mobile number")
//            return
//        } else if !(emailField.text ?? "").contains("@") && loginType == .email {
//            showToachMessage(message: "Please enter the correct email address")
//            return
//        }
//        emailTipViewIsHidden = loginType == .phone
//        
//        
//        func startTimer() {
//            guard timer == nil else { return }
//            
//            // after send success
//            var caculatorTime = totalTime
//            timer = Timer.scheduledTimer(withTimeInterval: 1, repeats: true, block: { _ in
//                caculatorTime -= 1
//                self.sendAuthButton.setTitle("Resend in \(caculatorTime)s", for: .normal)
//                if caculatorTime == -1 {
//                    self.timer?.invalidate()
//                    self.timer = nil
//                    self.sendAuthButton.setTitle("Resend in Send", for: .normal)
//                }
//            })
//            timer?.fire()
//        }
//        
//        let sendValidation = SendValidate(account: emailField.text, codeType: loginType, messageCodeType: .REGISTER)
//        UserInteractor.sendValidateCode(sendValidation) { success in
//            guard let s = success, s else { return }
//            startTimer()
//        }
//    }
    
    private func removeTimer() {
        if timer != nil {
            timer?.invalidate()
            timer = nil
        }
    }
    
    
    @IBAction func accountTypeSelectViewCloseButtonAction(_ sender: Any) {
        loginType = tempLoginType
        UIView.animate(withDuration: 0.35) {
            self.accountTypeSelectViewHidden = true
            self.view.layoutIfNeeded()
        }
    }
    
    @IBAction func accountTypeSelectViewEmailAction(_ sender: Any) {
        loginType = .email
    }
    
    @IBAction func accountTypeSelectViewMobileAction(_ sender: Any) {
        loginType = .phone
    }
    
    @IBAction func accountTypeSelectViewSaveAction(_ sender: Any) {
        UIView.animate(withDuration: 0.35) {
            self.accountTypeSelectViewHidden = true
            self.view.layoutIfNeeded()
        }
        emailTipViewIsHidden = loginType == .phone
        removeTimer()
//        sendAuthButton.setTitle("Get Code", for: .normal)
    }
}

extension SignUpViewController: UITextFieldDelegate {
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        textField.resignFirstResponder()
    }
    
    func textFieldDidChangeSelection(_ textField: UITextField) {
        checkLoginButtonStatus()
    }
    
    func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        
        if textField.tag == 3000 {
            guard loginType == .phone else { return true }
            let currentText = textField.text ?? ""
            let prospectiveText = (currentText as NSString).replacingCharacters(in: range, with: string)
            return true//prospectiveText.count <= (isProduce ? 10 : 11)
        } else if textField.tag == 4000 {
            let currentText = textField.text ?? ""
            let prospectiveText = (currentText as NSString).replacingCharacters(in: range, with: string)
            return prospectiveText.count <= 6
        } else {
            return true
        }
    }
}

extension SignUpViewController: UITextViewDelegate {
    func textView(_ textView: UITextView, shouldInteractWith URL: URL, in characterRange: NSRange, interaction: UITextItemInteraction) -> Bool {
        
        let range = (textView.text as NSString).range(of: privacyPolicy)
        if characterRange == range {
            AppInfoInteractor.fetchAppInfo(2) { result in
                guard let appInfo = result else { return }
                let webView = WebViewController(appInfo.url, contentString: appInfo.content)
                webView.title = self.privacyPolicy
                self.navigationController?.pushViewController(webView, animated: true)
            }
        } else {
            AppInfoInteractor.fetchAppInfo(3) { result in
                guard let appInfo = result else { return }
                let webView = WebViewController(appInfo.url, contentString: appInfo.content)
                webView.title = self.tAndcString
                self.navigationController?.pushViewController(webView, animated: true)
            }
        }
        
        return false
    }
}

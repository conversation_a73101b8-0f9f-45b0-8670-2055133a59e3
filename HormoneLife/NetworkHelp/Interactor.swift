//
//  Interactor.swift
//
//  Created by Tank on 2024/6/10.
//

import Foundation
import Alamofire
//import DFUnits

class AppInfoInteractor {
    
    //当前的baseURL: http://175.27.240.142:8087
    //图片传os， 时间格式
    
    static func fetchAppInfoList(completion: @escaping (AppInfoList) -> Void) {
        NetWorkHandle.requestAPI(.appInfoList) { (list: [AppInfo]?) in
            guard let list = list else { return }
            let infoList = AppInfoList(list: list)
            completion(infoList)
        }
    }
    
    //1：about us关于我们 2：privacy policy隐私政策 3:user agreement用户协议
    static func fetchAppInfo(_ type: Int, completion: @escaping (AppInfo?) -> Void) {

        NetWorkHandle.requestAPI(.appInfo(type: type)) { (result: AppInfo?) in
            completion(result)
        }
    }
}

class UserPeriodInteractor {
   
    static func userPeriodCycle(_ page: Int = 1, pageSize: Int = 1000, userId: String? = nil, userName: String? = nil, title: String? = nil, searchEndCycleTime: String? = nil, searchStartCycleTime: String? = nil, completion: @escaping (UserPeriodCycleList?) -> Void) {
        
        NetWorkHandle.requestAPI(.userPeriodCycle(page: page, pageSize: pageSize, userId: userId, userName: userName, title: title, searchEndCycleTime: searchEndCycleTime, searchStartCycleTime: searchStartCycleTime)) { (result: UserPeriodCycleList?) in
            completion(result)
        }
    }
    
    // 按年请求所有日历数据
    static func userPeriodCycleByYear(userId: String? = nil, userName: String? = nil, title: String? = nil, searchStartCycleTime: String? = nil, searchEndCycleTime: String? = nil, completion: @escaping ([UserPeriodCycleListByYear]) -> Void) {
        
        NetWorkHandle.requestAPI(.userPeriodCycleByYear(userId: userId, userName: userName, title: title, searchEndCycleTime: searchEndCycleTime, searchStartCycleTime: searchStartCycleTime)) { (results: [UserPeriodCycleListByYear]?) in
            
            // results: 按年的数组， ==>  [[2024, cycleList], [2023, cycleList]]
            guard let yearList = results else { return }
            completion(yearList)
        }
    }
    
    // 按月请求，返回每一天的详细标记情况 yyyy-MM-dd
    static func userPeriodCycleByMonth(startDate: String, toDate: String, completion: @escaping ([CalendarDatesDetail]) -> Void) {
        NetWorkHandle.requestAPI(.getDetailByMonth(startDate: startDate, endDate: toDate)) { (result: [CalendarDatesDetail]?) in
            guard let list = result else { return }
            completion(list)
        }
    }
}

let bindAccountUri = "/app/bindAccount"

class UserInteractor {
    //收不到otp 暂时用888888
    static func sendValidateCode(_ type: SendValidate, completion: @escaping (Bool?) -> Void) {
        NetWorkHandle.requestAPI(.sendValidate(type: type), completion: { (isSendSuccess: Bool?) in
            // API请求成功时，返回实际结果
            completion(isSendSuccess)
        }, failure: { error in
            // API请求失败时，返回false
            completion(false)
        })
    }
    
    //不通
    static func sendFeedback(_ feedback: FeedbackContent, completion: @escaping (FeedbackContent?) -> Void) {
        NetWorkHandle.requestAPI(.feedback(content: feedback)) { (content: FeedbackContent?) in
            completion(content)
        }
    }
    
    //登录, username & password不能为空, 返回的result 为token
    static func userLogin(username: String, password: String, deviceId: String = "", grantType: GrantType, code: String = "", uid: String? = nil, appleAccount: String? = nil, facebookAccount: String? = nil, googleAccount: String? = nil, completion: @escaping (String?) -> Void, failure: ((_ error: String?) -> Void)? = nil) {
        
        
        NetWorkHandle.requestAPI(.login(deviceId: deviceId, password: password, username: username, grantType: grantType, code: code, uid: uid, appleAccount: appleAccount, facebookAccount: facebookAccount, googleAccount: googleAccount)) { (result: String?) in
            completion(result)
        } failure: { error in
            failure?(error)
        }
    }
    
    //登出
    static func userLogout(completion: @escaping (Bool?) -> Void) {
        NetWorkHandle.requestAPI(.logout) { (result: Bool?) in
            completion(result ?? true)
        }
    }
    
    // account 和 email/phone是不是重复了
    static func registerByCode(account: String, code: String, codeType: LoginType, firstName: String, lastName: String, password: String, invitationCode: String = "", deviceId: String = UserDefaults.standard.deviceID ?? "", completion: @escaping (String?) -> Void) {
        NetWorkHandle.requestAPI(.registerByCode(account: account, area: "", code: code, codeType: codeType, deviceId: deviceId, email: codeType == .email ? account : "", phone: codeType == .phone ? account : "", firstName: firstName, userName: lastName, password: password, inviteCode: invitationCode)) { (result: String?) in
            completion(result)
        }
    }
    
    // registerByTourist 根据设备Id注册游客返回token = result,  deviceId来自哪里 - uuid
    static func registerByTourist(completion: @escaping (String?) -> Void) {
        let deviceId = UserDefaults.standard.deviceID ?? ""
        NetWorkHandle.requestAPI(.registerByTourist(deviceId: deviceId)) { (result: String?) in
            completion(result)
        }
    }
    
    static func getUserInfo(_ completion: @escaping (UserInfo?) -> Void) {
        NetWorkHandle.requestAPI(.getUserInfo) { (result: UserInfo?) in
            completion(result)
        }
    }
    
    static func getCycleAndPeriodAvg(_ completion: @escaping (CycleAndPeriodAvg?) -> Void) {
        NetWorkHandle.requestAPI(.getCycleAndPeriodAvg) { (result: CycleAndPeriodAvg?) in
            completion(result)
        }
    }
    
    static func setNewPassword(_ account: String, code: String, codeType: LoginType, newPassword: String, completion: @escaping (Bool?) -> Void) {
        NetWorkHandle.requestAPI(.findPassword(account: account, code: code, codeType: codeType, newPassword: newPassword)) { (result: Bool?) in
            completion(result ?? true)
        }
    }
    
    //需要登录
    static func userConfigUpdate(_ config: UserConfig, completion: @escaping (Bool) -> Void) {
        NetWorkHandle.requestAPI(.configUpdate(config: config)) { (result: Bool?) in
            completion(result ?? true)
        }
    }
    
    static func bindEmail(_ email: String, code: String, completion: @escaping (Bool) -> Void) {
        NetWorkHandle.requestAPI(.bindEmail(email: email, code: code)) { (result: Bool?) in
            completion(result ?? true)
        }
    }
    
    static func bindPhone(_ phone: String, code: String, completion: @escaping (Bool) -> Void) {
        NetWorkHandle.requestAPI(.bindPhone(phone: phone, code: code)) { (result: Bool?) in
            completion(result ?? true)
        }
    }
    
    static func unBindThirdPartAccount(_ grantType: String, completion: @escaping (Bool) -> Void) {
        NetWorkHandle.requestAPI(.unBindThirdPartAccount(grantType: grantType)) { (result: Bool?) in
            completion(result ?? true)
        }
    }

    //注销账号
    static func logOffAcount(_ account: String, code: String, codeType: LoginType, completion: @escaping (Bool) -> Void) {
        NetWorkHandle.requestAPI(.logOff(account: account, code: code, codeType: codeType)) { (result: Bool?) in
            completion(result ?? true)
        }
    }
    
    //切换角色 1 get pregnant 2 cycle tracking, 需要登录
    static func userUpdateRole(_ roleType: RoleType, completion: @escaping (Bool) -> Void) {
        NetWorkHandle.requestAPI(.roleUpdate(roleType: roleType)) { (result: Bool?) in
            completion(result ?? true)
        }
    }
    
    static func updateUserInfo(userName: String, firstName: String, sex: Int, birthday: String, headImg: String, label: String, photos: String, completion: @escaping (Bool?) -> Void) {
        NetWorkHandle.requestAPI(.updateUserInfo(userName: userName, firstName: firstName, sex: sex, birthday: birthday, headImg: headImg, label: label, photos: photos)) { (result: Bool?) in
            completion(result)
        }
    }
    
    //bind acount
    static func bindThirdAccount(params: [String : Any]?, success: @escaping (_ result: [String : Any]?) -> Void,failure: @escaping (_ error: String?) -> Void) {
        print("params = \(params)")
        NetWorkHandle.requestPost(bindAccountUri, parameters: params) { result in
            success(result)
        } failure: { error in
            failure(error)
        }
    }
    
    //分页列表用在哪里
    //月经流血统计 需要传那么多参数吗
}

let getTipUri = "/app/home/<USER>"

class HomeInteractor {
    //登录用户才通，需要token, postman直接请求返回没有data键
    static func fetchHomeData(_ completion: @escaping (HomeData?) -> Void) {
        NetWorkHandle.requestAPI(.homeData) { (result: HomeData?) in
            completion(result)
        }
    }
    
    // startup page, banner
    static func fetchAdOnPosition(_ position: AdvertisementType, completion: @escaping ([Advertisement]) -> Void) {
        NetWorkHandle.requestAPI(.ad(position: position)) { (list: [Advertisement]?) in
            guard let list = list else { return }
//            let adList = AdList(data: list)
            completion(list)
        }
    }
    
    static func getTip(success: @escaping (_ result: HomeTipModel?) -> Void,failure: @escaping (_ error: String?) -> Void) {
        
        NetWorkHandle.requestGet(getTipUri, parameters: nil) { result in
            let model = HomeTipModel.deserialize(from: result)
            success(model)
        } failure: { error in
            failure(error)
        }
    }
}

class UserNoteInteractor {
    
    //add
    static func addNewNote(_ note: Note, completion: @escaping (Bool) -> Void) {
        NetWorkHandle.requestAPI(.addNote(newNote: note)) { (result: Bool?) in
            completion(result ?? true)
        }
    }
    
    static func updateNote(_ note: Note, completion: @escaping (Bool) -> Void) {
        NetWorkHandle.requestAPI(.updateNote(newNote: note)) { (result: Bool?) in
            completion(result ?? true)
        }
    }
    
    static func fetchNoteDetailById(_ noteId: Int, completion: @escaping (Note?) -> Void) {
        NetWorkHandle.requestAPI(.noteDetail(id: noteId)) { (result: Note?) in
            completion(result)
        }
    }
    
    static func deleteNoteById(_ noteId: Int, completion: @escaping (Bool) -> Void) {
        NetWorkHandle.requestAPI(.deleteNote(id: noteId)) { (result: Bool?) in
            completion(result ?? true)
        }
    }
}

class UserMessageInteractor {
    static func fetchMessages(_ page: Int = 1, pageSize: Int = 100, type: MessageType = .system, isRead: Int = 2, completion: @escaping (MessageList?) -> Void) {
        NetWorkHandle.requestAPI(.messages(page: page, pageSize: pageSize, type: type, isRead: isRead)) { (result: MessageList?) in
            completion(result)
        }
    }
    
    static func readMessageDetailById(_ messageId: String, completion: @escaping (Message?) -> Void) {
        NetWorkHandle.requestAPI(.message(messageId: messageId)) { (result: Message?) in
            completion(result)
        }
    }
    
    static func markAsReadById(_ messageId: String, completion: @escaping (Int) -> Void) {
        NetWorkHandle.requestAPI(.readMessage(messageId: messageId)) { (result: Int?) in
            completion(result ?? 0)
        }
    }
    
    static func deleteMessageById(_ messageId: String, completion: @escaping (Bool) -> Void) {
        NetWorkHandle.requestAPI(.deleteMessage(messageId: messageId)) { (result: Bool?) in
            completion(result ?? false)
        }
    }
}
